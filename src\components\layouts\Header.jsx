import React, { useEffect, useState } from 'react';
import { Menu as MenuIcon } from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import { <PERSON><PERSON>, Drawer, Menu, Space } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import useAuth from '@/hooks/useAuth';
import useDeviceDetect from '@/hooks/useDeviceDetect';
import { useColorModeStore } from '@/store/colorMode.store';
import { AUTH_NAV_ITEMS, getDashboardPath, MAIN_NAV_ITEMS } from '@/utils/constants';
import ColorModeToggle from '../shared/ColorModeToggle';
import StyledUserButton from '../shared/StyledUserButton';
import { logo_lite, logo_dark } from '../../assets';

const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, role } = useAuth();
  const { isMobile, isDesktop } = useDeviceDetect();
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const showDrawer = () => setDrawerVisible(true);
  const closeDrawer = () => setDrawerVisible(false);

  const dashboardPath = getDashboardPath(role);

  // Create menu items from MAIN_NAV_ITEMS (no children)
  const mainMenuItems = MAIN_NAV_ITEMS.map((item) => ({
    key: item.key,
    label: (
      <a
        to={item.key}
        className="no-underline hover:no-underline"
      >
        {item.label}
      </a>
    ),
    // icon: item.icon ? <LucideIcon icon={item.icon} /> : null,
  }));

  // Create mobile menu items (can include additional items specific to mobile)
  const mobileMenuItems = [
    ...mainMenuItems,
    // Add login/register buttons to mobile menu if user is not authenticated
    ...(user
      ? []
      : AUTH_NAV_ITEMS.unauthenticated.map((item) => ({
          key: item.key,
          label: (
            <Link
              to={item.key}
              className="no-underline hover:no-underline"
            >
              {item.label}
            </Link>
          ),
          icon: item.icon ? <LucideIcon icon={item.icon} /> : null,
        }))),
  ];

  // Theme class is applied in App.jsx to avoid conflicts

  return (
    <motion.header
      className="sticky top-0 z-50 bg-card/80 backdrop-blur-md"
      initial={{ height: 80, padding: '1rem 2rem' }}
      animate={{
        height: isScrolled ? 60 : 80,
        padding: isScrolled ? '0.5rem 1rem' : '1rem 2rem',
        width: isScrolled ? '80%' : '100%',
        margin: isScrolled ? '0.5rem auto' : '0',
        top: isScrolled ? '10px' : '0',
        borderRadius: isScrolled ? '1rem' : '0',
        boxShadow: isScrolled
          ? isDark
            ? '0 8px 32px rgba(0, 0, 0, 0.3)'
            : '0 8px 32px rgba(0, 0, 0, 0.1)'
          : 'none',
      }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
    >
      <div className="flex items-center justify-between w-full max-w-7xl mx-auto">
        {/* Logo */}
        <Link
          to="/"
          className="flex items-center z-10"
        >
          <img
            src={isDark ? logo_dark : logo_lite}
            alt="logo"
            className="w-28 sm:w-36"
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden lg:block flex-grow max-w-xl mx-8">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            className="flex justify-center"
            items={mainMenuItems}
          />
        </div>

        {/* Auth Buttons and Color Mode Toggle */}
        <div className="flex items-center gap-2 sm:gap-3">
          {user ? (
            // <Tooltip title={AUTH_NAV_ITEMS.authenticated.description}>
            <Button
              type="primary"
              shape="round"
              size={isMobile ? 'small' : 'large'}
              icon={<LucideIcon icon={AUTH_NAV_ITEMS.authenticated.icon} />}
              onClick={() => navigate(dashboardPath)}
            >
              {AUTH_NAV_ITEMS.authenticated.label}
            </Button>
          ) : (
            // </Tooltip>
            // Only show login/register buttons on desktop
            !isMobile && (
              <Space size={isMobile ? 2 : 3}>
                {AUTH_NAV_ITEMS.unauthenticated.map((item) => (
                  <Button
                    shape="round"
                    key={item.key}
                    type={item.key === 'get-started' ? 'primary' : 'default'}
                    size={isMobile ? 'middle' : 'large'}
                    icon={<LucideIcon icon={item.icon} />}
                    onClick={() => navigate(item.key)}
                  >
                    {item.label}
                  </Button>
                ))}
              </Space>
            )
          )}
          {user && <StyledUserButton size={isScrolled ? 'small' : 'default'} />}
          {/* Color Mode Toggle */}
          <ColorModeToggle />

          {/* Mobile Menu Button - Only show on mobile */}
          {!isDesktop && (
            <Button
              className="ml-1 sm:ml-2"
              type="text"
              icon={
                <LucideIcon
                  icon={MenuIcon}
                  className="text-foreground"
                />
              }
              onClick={showDrawer}
            />
          )}
        </div>
      </div>

      {/* Mobile Drawer - Only render on mobile */}
      {!isDesktop && (
        <Drawer
          title={
            <div className="flex items-center justify-between">
              <img
                src={isDark ? logo_dark : logo_lite}
                alt="logo"
                className="w-28"
              />
              <ColorModeToggle />
            </div>
          }
          placement="right"
          onClose={closeDrawer}
          open={drawerVisible}
          width="80%"
          styles={{
            body: { padding: 0 },
          }}
        >
          <Menu
            mode="vertical"
            selectedKeys={[location.pathname]}
            className="border-0"
            items={mobileMenuItems}
          />
        </Drawer>
      )}
    </motion.header>
  );
};

export default Header;
