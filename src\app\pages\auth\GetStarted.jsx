import { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, Row, Col, message } from 'antd';
import { UserOutlined, MailOutlined, PhoneOutlined, BankOutlined, MessageOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

const GetStarted = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      // Here you would typically send the data to your backend
      console.log('Form submitted:', values);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('Thank you for your interest! We will contact you soon.');
      form.resetFields();
    } catch (error) {
      message.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          className="text-center mb-12"
        >
          <Title level={1} className="mb-4">
            Get Started with Wurk
          </Title>
          <Paragraph className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Ready to transform your interview process? Fill out the form below and our team will reach out to help you get started.
          </Paragraph>
        </motion.div>

        <Row gutter={[32, 32]} align="middle">
                    {/* Info Section */}
                    <Col xs={24} lg={10}>
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0, x: 20 },
                visible: { opacity: 1, x: 0, transition: { duration: 0.6, delay: 0.4 } },
              }}
              className="space-y-8"
            >
              <div className="text-center lg:text-left">
                <Title level={3} className="mb-4">
                  Why Choose Wurk?
                </Title>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold">1</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-2">Expert Interviewers</Title>
                      <Paragraph className="text-gray-600 dark:text-gray-300">
                        Access a network of experienced professionals who can conduct technical and behavioral interviews.
                      </Paragraph>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold">2</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-2">Streamlined Process</Title>
                      <Paragraph className="text-gray-600 dark:text-gray-300">
                        Simplify your hiring workflow with our intuitive platform and automated scheduling.
                      </Paragraph>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold">3</span>
                    </div>
                    <div>
                      <Title level={5} className="mb-2">Quality Insights</Title>
                      <Paragraph className="text-gray-600 dark:text-gray-300">
                        Get detailed feedback and assessments to make informed hiring decisions.
                      </Paragraph>
                    </div>
                  </div>
                </div>
              </div>

              <Card className="bg-gradient-to-r from-primary to-blue-600 text-white border-0">
                <div className="text-center">
                  <Title level={4} className="text-white mb-2">
                    Ready to Get Started?
                  </Title>
                  <Paragraph className="text-blue-100 mb-0">
                    Join hundreds of companies already using Wurk to streamline their interview process.
                  </Paragraph>
                </div>
              </Card>
            </motion.div>
          </Col>
          
          {/* Form Section */}
          <Col xs={24} lg={14}>
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0, x: -20 },
                visible: { opacity: 1, x: 0, transition: { duration: 0.6, delay: 0.2 } },
              }}
            >
              <Card className="shadow-lg rounded-xl border-0">
                <Title level={3} className="mb-6 text-center">
                  Tell Us About Your Company
                </Title>
                
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                  size="large"
                  requiredMark={false}
                >
                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fullName"
                        label="Full Name"
                        rules={[
                          { required: true, message: 'Please enter your full name' },
                          { min: 2, message: 'Name must be at least 2 characters' }
                        ]}
                      >
                        <Input
                          prefix={<UserOutlined className="text-gray-400" />}
                          placeholder="Enter your full name"
                          className="rounded-lg"
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="workEmail"
                        label="Work Email"
                        rules={[
                          { required: true, message: 'Please enter your work email' },
                          { type: 'email', message: 'Please enter a valid email address' }
                        ]}
                      >
                        <Input
                          prefix={<MailOutlined className="text-gray-400" />}
                          placeholder="Enter your work email"
                          className="rounded-lg"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="phone"
                        label="Phone Number"
                        rules={[
                          { required: true, message: 'Please enter your phone number' },
                          { pattern: /^[\+]?[1-9][\d]{0,15}$/, message: 'Please enter a valid phone number' }
                        ]}
                      >
                        <Input
                          prefix={<PhoneOutlined className="text-gray-400" />}
                          placeholder="Enter your phone number"
                          className="rounded-lg"
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="companyName"
                        label="Company Name"
                        rules={[
                          { required: true, message: 'Please enter your company name' },
                          { min: 2, message: 'Company name must be at least 2 characters' }
                        ]}
                      >
                        <Input
                          prefix={<BankOutlined className="text-gray-400" />}
                          placeholder="Enter your company name"
                          className="rounded-lg"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    name="message"
                    label="Message (Optional)"
                    rules={[
                      { max: 500, message: 'Message cannot exceed 500 characters' }
                    ]}
                  >
                    <TextArea
                      prefix={<MessageOutlined className="text-gray-400" />}
                      placeholder="Tell us about your hiring needs, team size, or any specific requirements..."
                      rows={4}
                      className="rounded-lg"
                      showCount
                      maxLength={500}
                    />
                  </Form.Item>

                  <Form.Item className="mb-0">
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      size="large"
                      className="w-full h-12 rounded-lg font-medium"
                    >
                      {loading ? 'Submitting...' : 'Get Started'}
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </motion.div>
          </Col>


        </Row>
      </div>
    </div>
  );
};

export default GetStarted;
