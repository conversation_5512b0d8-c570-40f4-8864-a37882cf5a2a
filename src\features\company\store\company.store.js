/**
 * Company Store - STANDARDIZED VERSION
 *
 * Responsibilities:
 * - Job postings management
 * - Application reviews and processing
 * - Candidate shortlisting
 * - Company-specific workflows
 * - Hiring analytics
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Common action patterns
 *
 * Does NOT handle:
 * - Authentication (handled by auth store)
 * - Profile data (handled by auth store)
 */

import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import { getCompanyApplications, getShortlistedCandidates } from '../services/company.service';

// Initial state following standardized pattern
const initialCompanyState = {
  // === CORE DATA STATE ===
  // Applications state
  applications: [],

  // Shortlisted candidates state
  shortlistedCandidates: [],

  // === STANDARDIZED UI STATE ===
  // Main loading/error states
  loading: false,
  error: null,

  // Specific loading states for complex operations
  applicationsLoading: false,
  shortlistedLoading: false,

  // Specific error states for better error handling
  applicationsError: null,
  shortlistedError: null,

  // === STANDARDIZED CACHE MANAGEMENT ===
  _cache: {
    lastApplicationsFetch: null,
    lastShortlistedFetch: null,
    applicationsExpiry: 2 * 60 * 1000, // 2 minutes (frequent updates)
    shortlistedExpiry: 10 * 60 * 1000, // 10 minutes
  },
};

const useCompanyStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialCompanyState,

        // === STANDARDIZED UI STATE HELPERS ===
        setLoading: (loading) => set({ loading }, false, 'company:setLoading'),
        setError: (error) => set({ error }, false, 'company:setError'),
        clearError: () => set({ error: null }, false, 'company:clearError'),

        setApplicationsLoading: (loading) =>
          set({ applicationsLoading: loading }, false, 'company:setApplicationsLoading'),
        setApplicationsError: (error) =>
          set({ applicationsError: error }, false, 'company:setApplicationsError'),
        clearApplicationsError: () =>
          set({ applicationsError: null }, false, 'company:clearApplicationsError'),

        // === STANDARDIZED CACHE HELPERS ===

        updateApplicationsCache: () => {
          const { _cache } = get();
          set(
            {
              _cache: { ..._cache, lastApplicationsFetch: Date.now() },
            },
            false,
            'company:updateApplicationsCache'
          );
        },

        isApplicationsCacheValid: () => {
          const { _cache } = get();
          return (
            _cache.lastApplicationsFetch &&
            Date.now() - _cache.lastApplicationsFetch < _cache.applicationsExpiry
          );
        },

        // === APPLICATION MANAGEMENT ACTIONS ===

        fetchCompanyApplications: async (id) => {
          set({ isApplicationsLoading: true, applicationsError: null });
          try {
            const { success, data, error } = await getCompanyApplications(id);
            if (success) {
              set({ applications: data });
            } else {
              set({ applicationsError: error });
            }
          } catch (error) {
            set({ applicationsError: error.message });
          } finally {
            set({ isApplicationsLoading: false });
          }
        },

        fetchShortlistedCandidates: async (id) => {
          set({ isShortlistedLoading: true, shortlistedError: null });
          try {
            const { success, data, error } = await getShortlistedCandidates(id);
            if (success) {
              set({ shortlistedCandidates: data });
            } else {
              set({ shortlistedError: error });
            }
          } catch (error) {
            set({ shortlistedError: error.message });
          } finally {
            set({ isShortlistedLoading: false });
          }
        },

        // === STANDARDIZED RESET METHOD ===
        resetStore: () => {
          set(initialCompanyState, false, 'company:resetStore');
        },
      }),
      {
        name: 'company-storage',
        partialize: (state) => ({
          applications: state.applications,
          shortlistedCandidates: state.shortlistedCandidates,
          _cache: state._cache,
        }),
      }
    ),
    { name: 'company-store' }
  )
);

export default useCompanyStore;
