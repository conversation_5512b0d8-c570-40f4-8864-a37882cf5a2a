import React from 'react';
import { Card, Button, Space } from 'antd';
import { AuditOutlined, ExportOutlined } from '@ant-design/icons';

const AuditLogs = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Audit Logs
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track all system activities and changes
          </p>
        </div>
        <Space>
          <Button icon={<ExportOutlined />}>
            Export Logs
          </Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <AuditOutlined className="text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Audit Logs Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will provide comprehensive audit trail and activity logging.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default AuditLogs;
