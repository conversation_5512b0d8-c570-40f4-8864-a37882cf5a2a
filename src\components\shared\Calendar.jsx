/**
 * Enhanced Calendar Component with Google Calendar Integration
 *
 * Features:
 * - Google Calendar sync
 * - Google Meet integration
 * - Event management (CRUD)
 * - Multi-role support
 * - Responsive design
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar as AntCalendar,
  Badge,
  Card,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  Row,
  Col,
  Typography,
  Tooltip,
  Tabs,
  List,
  Tag,
  Space,
  Divider,
  Switch,
  Alert,
  Spin,
  Avatar,
  Popover,
} from 'antd';
import {
  Plus,
  Calendar as CalendarIcon,
  Clock,
  User,
  Users,
  Video,
  MapPin,
  Info,
  Trash2,
  Edit,
  Globe,
  RefreshCw,
  Link,
  X,
} from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import dayjs from 'dayjs';
import useCalendar from '@/hooks/useCalendar';
import useDeviceDetect from '@/hooks/useDeviceDetect';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

/**
 * Enhanced Calendar component with Google integration
 */
const Calendar = ({
  userType = 'candidate',
  eventTypes = [],
  participants = [],
  viewOptions = { month: true, week: true, day: true, agenda: true },
}) => {
  const { isMobile, isTablet } = useDeviceDetect();
  const {
    events,
    loading,
    syncing,
    error,
    googleUser,
    isGoogleConnected,
    lastSyncTime,
    connectGoogleCalendar,
    disconnectGoogleCalendar,
    syncWithGoogleCalendar,
    createEvent,
    updateEvent,
    deleteEvent,
    createInstantMeeting,
    clearError,
  } = useCalendar();

  // Local state
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState('add');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [calendarView, setCalendarView] = useState('month');
  const [form] = Form.useForm();

  // Default event types
  const defaultEventTypes = [
    { id: 'meeting', name: 'Meeting', color: 'green' },
    { id: 'deadline', name: 'Deadline', color: 'red' },
    { id: 'reminder', name: 'Reminder', color: 'orange' },
    { id: 'assessment', name: 'Assessment', color: 'purple' },
    { id: 'call', name: 'Call', color: 'blue' },
  ];

  const displayEventTypes = eventTypes.length > 0 ? eventTypes : defaultEventTypes;

  // Get events for a specific date
  const getEventsForDate = (date) => {
    return events.filter(
      (event) => dayjs(event.date).format('YYYY-MM-DD') === date.format('YYYY-MM-DD')
    );
  };

  // Handle Google Calendar connection
  const handleGoogleConnect = async () => {
    const result = await connectGoogleCalendar();
    if (result.success) {
      // Auto-sync after connection
      setTimeout(() => {
        syncWithGoogleCalendar();
      }, 1000);
    }
  };

  // Handle Google Calendar sync
  const handleGoogleSync = async () => {
    await syncWithGoogleCalendar();
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    if (calendarView !== 'day') {
      setCalendarView('day');
    }
  };

  // Handle date cell render
  const dateCellRender = (date) => {
    const dateEvents = getEventsForDate(date);

    return (
      <ul className="events-list p-0 m-0 list-none">
        {dateEvents.slice(0, 3).map((event) => (
          <li
            key={event.id}
            onClick={(e) => {
              e.stopPropagation();
              handleEventClick(event);
            }}
            className="cursor-pointer"
          >
            <Badge
              color={
                event.type ? displayEventTypes.find((t) => t.id === event.type)?.color : 'blue'
              }
              text={
                <Tooltip title={event.title}>
                  <span className="truncate block text-xs">
                    {isMobile
                      ? event.title.length > 6
                        ? `${event.title.substring(0, 6)}...`
                        : event.title
                      : event.title.length > 12
                        ? `${event.title.substring(0, 12)}...`
                        : event.title}
                  </span>
                </Tooltip>
              }
            />
          </li>
        ))}
        {dateEvents.length > 3 && (
          <li className="text-xs text-gray-500 mt-1">+{dateEvents.length - 3} more</li>
        )}
      </ul>
    );
  };

  // Handle event click
  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setModalMode('view');
    setModalVisible(true);
  };

  // Handle add event
  const handleAddEvent = () => {
    setModalMode('add');
    setSelectedEvent(null);
    form.setFieldsValue({
      title: '',
      description: '',
      type: displayEventTypes[0]?.id,
      date: selectedDate,
      time: dayjs().hour(9).minute(0),
      duration: 60,
      location: '',
      participants: [],
      isOnline: true,
      meetingLink: '',
      syncToGoogle: isGoogleConnected,
      createMeeting: false,
    });
    setModalVisible(true);
  };

  // Handle form submission
  const handleFormSubmit = async (values) => {
    const eventData = {
      ...values,
      date: values.date.format('YYYY-MM-DD'),
      time: values.time.format('HH:mm'),
    };

    const options = {
      syncToGoogle: values.syncToGoogle,
      createMeeting: values.createMeeting,
    };

    let result;
    if (modalMode === 'add') {
      result = await createEvent(eventData, options);
    } else if (modalMode === 'edit') {
      result = await updateEvent(selectedEvent.id, eventData, options);
    }

    if (result.success) {
      setModalVisible(false);
      form.resetFields();
    }
  };

  // Handle event deletion
  const handleDeleteEvent = async () => {
    if (selectedEvent) {
      const result = await deleteEvent(selectedEvent.id, { syncToGoogle: true });
      if (result.success) {
        setModalVisible(false);
        form.resetFields();
      }
    }
  };

  // Handle instant meeting creation
  const handleInstantMeeting = async () => {
    const result = await createInstantMeeting({
      title: 'Instant Meeting',
      description: 'Quick meeting created from calendar',
      attendees: [],
    });

    if (result.success) {
      window.open(result.data.meetingLink, '_blank');
    }
  };

  // Render Google Calendar integration panel
  const renderGoogleIntegration = () => (
    <Card
      size="small"
      className="mb-4"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <LucideIcon
            icon={Globe}
            className="text-blue-500 mr-2"
          />
          <div>
            <Text strong>Google Calendar</Text>
            {isGoogleConnected && (
              <div className="text-xs text-gray-500">Connected as {googleUser?.email}</div>
            )}
          </div>
        </div>

        <Space>
          {isGoogleConnected ? (
            <>
              <Button
                size="small"
                icon={
                  <LucideIcon
                    icon={RefreshCw}
                    className={syncing ? 'animate-spin' : ''}
                  />
                }
                onClick={handleGoogleSync}
                disabled={syncing}
              >
                Sync
              </Button>
              <Button
                size="small"
                icon={<LucideIcon icon={Video} />}
                onClick={handleInstantMeeting}
                type="primary"
              >
                Instant Meet
              </Button>
              <Button
                size="small"
                icon={<LucideIcon icon={X} />}
                onClick={disconnectGoogleCalendar}
                danger
              >
                Disconnect
              </Button>
            </>
          ) : (
            <Button
              size="small"
              icon={<LucideIcon icon={Globe} />}
              onClick={handleGoogleConnect}
              type="primary"
            >
              Connect Google
            </Button>
          )}
        </Space>
      </div>

      {lastSyncTime && (
        <div className="text-xs text-gray-500 mt-2">
          Last synced: {dayjs(lastSyncTime).format('MMM D, YYYY h:mm A')}
        </div>
      )}
    </Card>
  );

  // Render event details
  const renderEventDetails = (event) => {
    if (!event) return null;

    const eventType = displayEventTypes.find((t) => t.id === event.type);

    return (
      <div className="event-details">
        <div className="flex items-center mb-4">
          <Badge color={eventType?.color || 'blue'} />
          <Text
            strong
            className="ml-2"
          >
            {eventType?.name || 'Event'}
          </Text>
        </div>

        <Title level={4}>{event.title}</Title>

        <div className="event-info mt-4">
          <div className="flex items-start mb-3">
            <LucideIcon
              icon={CalendarIcon}
              className="mt-1 mr-2"
            />
            <div>
              <div>{dayjs(event.date).format('dddd, MMMM D, YYYY')}</div>
              <div className="flex items-center mt-1">
                <LucideIcon
                  icon={Clock}
                  className="mr-2"
                />
                <span>{dayjs(event.time, 'HH:mm').format('h:mm A')}</span>
                <span className="mx-2">•</span>
                <span>{event.duration} minutes</span>
              </div>
            </div>
          </div>

          {(event.location || event.meetingLink) && (
            <div className="flex items-start mb-3">
              <LucideIcon
                icon={MapPin}
                className="mt-1 mr-2"
              />
              <div>
                {event.isOnline ? (
                  <>
                    <div>Online Meeting</div>
                    {event.meetingLink && (
                      <a
                        href={event.meetingLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary"
                      >
                        Join Meeting
                      </a>
                    )}
                  </>
                ) : (
                  <div>{event.location}</div>
                )}
              </div>
            </div>
          )}

          {event.participants && event.participants.length > 0 && (
            <div className="flex items-start mb-3">
              <LucideIcon
                icon={Users}
                className="mt-1 mr-2"
              />
              <div>
                <div className="mb-1">Participants:</div>
                <div className="flex flex-wrap gap-1">
                  {event.participants.map((participant, index) => (
                    <Tag key={index}>{participant}</Tag>
                  ))}
                </div>
              </div>
            </div>
          )}

          {event.description && (
            <div className="flex items-start mb-3">
              <LucideIcon
                icon={Info}
                className="mt-1 mr-2"
              />
              <div>
                <div className="mb-1">Description:</div>
                <Paragraph>{event.description}</Paragraph>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render day view
  const renderDayView = () => {
    const dayEvents = getEventsForDate(selectedDate);

    return (
      <div className="day-view">
        <Title
          level={4}
          className="mb-4"
        >
          {selectedDate.format('dddd, MMMM D, YYYY')}
        </Title>

        {dayEvents.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={dayEvents.sort((a, b) =>
              dayjs(a.time, 'HH:mm').diff(dayjs(b.time, 'HH:mm'))
            )}
            renderItem={(event) => {
              const eventType = displayEventTypes.find((t) => t.id === event.type);

              return (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => handleEventClick(event)}
                    >
                      View
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge
                        color={eventType?.color || 'blue'}
                        style={{ width: '10px', height: '10px' }}
                      />
                    }
                    title={
                      <div className="flex items-center">
                        <span className="mr-3">{dayjs(event.time, 'HH:mm').format('h:mm A')}</span>
                        <span>{event.title}</span>
                      </div>
                    }
                    description={
                      <div className="flex items-center text-xs">
                        <span>{event.duration} min</span>
                        {(event.location || event.meetingLink) && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{event.isOnline ? 'Online Meeting' : event.location}</span>
                          </>
                        )}
                        {event.participants && event.participants.length > 0 && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{event.participants.length} participants</span>
                          </>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">No events scheduled for this day</Text>
            <div className="mt-4">
              <Button
                type="primary"
                icon={<LucideIcon icon={Plus} />}
                onClick={handleAddEvent}
              >
                Add Event
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render agenda view
  const renderAgendaView = () => {
    // Group events by date
    const groupedEvents = events.reduce((acc, event) => {
      const dateKey = dayjs(event.date).format('YYYY-MM-DD');
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(event);
      return acc;
    }, {});

    // Sort dates
    const sortedDates = Object.keys(groupedEvents).sort((a, b) => dayjs(a).diff(dayjs(b)));

    return (
      <div className="agenda-view">
        <Title
          level={4}
          className="mb-4"
        >
          Upcoming Events
        </Title>

        {sortedDates.length > 0 ? (
          sortedDates.map((dateKey) => (
            <div
              key={dateKey}
              className="mb-6"
            >
              <div className="date-header mb-3 pb-2 border-b">
                <Text strong>{dayjs(dateKey).format('dddd, MMMM D, YYYY')}</Text>
              </div>

              <List
                itemLayout="horizontal"
                dataSource={groupedEvents[dateKey].sort((a, b) =>
                  dayjs(a.time, 'HH:mm').diff(dayjs(b.time, 'HH:mm'))
                )}
                renderItem={(event) => {
                  const eventType = displayEventTypes.find((t) => t.id === event.type);

                  return (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          onClick={() => handleEventClick(event)}
                        >
                          View
                        </Button>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Badge
                            color={eventType?.color || 'blue'}
                            style={{ width: '10px', height: '10px' }}
                          />
                        }
                        title={
                          <div className="flex items-center">
                            <span className="mr-3">
                              {dayjs(event.time, 'HH:mm').format('h:mm A')}
                            </span>
                            <span>{event.title}</span>
                          </div>
                        }
                        description={
                          <div className="flex items-center text-xs">
                            <span>{event.duration} min</span>
                            {(event.location || event.meetingLink) && (
                              <>
                                <span className="mx-2">•</span>
                                <span>{event.isOnline ? 'Online Meeting' : event.location}</span>
                              </>
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  );
                }}
              />
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <Text type="secondary">No upcoming events</Text>
            <div className="mt-4">
              <Button
                type="primary"
                icon={<LucideIcon icon={Plus} />}
                onClick={handleAddEvent}
              >
                Add Event
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="calendar-container">
      {/* Error Alert */}
      {error && (
        <Alert
          message="Calendar Error"
          description={error}
          type="error"
          closable
          onClose={clearError}
          className="mb-4"
        />
      )}

      {/* Google Integration Panel */}
      {renderGoogleIntegration()}

      {/* Calendar Header */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Title
            level={4}
            className="m-0"
          >
            Calendar
          </Title>

          <Space>
            <Button
              type="primary"
              icon={<LucideIcon icon={Plus} />}
              onClick={handleAddEvent}
              loading={loading}
            >
              Add Event
            </Button>
          </Space>
        </div>

        {/* Calendar Views */}
        <Tabs
          activeKey={calendarView}
          onChange={setCalendarView}
          items={[
            viewOptions.month && {
              key: 'month',
              label: 'Month',
              children: (
                <AntCalendar
                  value={selectedDate}
                  onSelect={handleDateSelect}
                  dateCellRender={dateCellRender}
                  className="custom-calendar"
                />
              ),
            },
            viewOptions.day && {
              key: 'day',
              label: 'Day',
              children: renderDayView(),
            },
            viewOptions.agenda && {
              key: 'agenda',
              label: 'Agenda',
              children: renderAgendaView(),
            },
          ].filter(Boolean)}
        />
      </Card>

      {/* Event Modal */}
      <Modal
        title={
          modalMode === 'add'
            ? 'Add New Event'
            : modalMode === 'edit'
              ? 'Edit Event'
              : 'Event Details'
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={
          modalMode === 'view' ? (
            <Space>
              <Button onClick={() => setModalVisible(false)}>Close</Button>
              <Button
                type="primary"
                icon={<LucideIcon icon={Edit} />}
                onClick={() => {
                  setModalMode('edit');
                  form.setFieldsValue({
                    title: selectedEvent.title,
                    description: selectedEvent.description,
                    type: selectedEvent.type,
                    date: dayjs(selectedEvent.date),
                    time: dayjs(selectedEvent.time, 'HH:mm'),
                    duration: selectedEvent.duration,
                    location: selectedEvent.location,
                    participants: selectedEvent.participants,
                    isOnline: selectedEvent.isOnline,
                    meetingLink: selectedEvent.meetingLink,
                    syncToGoogle: isGoogleConnected,
                    createMeeting: false,
                  });
                }}
              >
                Edit
              </Button>
              <Button
                danger
                icon={<LucideIcon icon={Trash2} />}
                onClick={handleDeleteEvent}
                loading={loading}
              >
                Delete
              </Button>
            </Space>
          ) : null
        }
        width={isMobile ? '95%' : 600}
      >
        {modalMode === 'view' ? (
          renderEventDetails(selectedEvent)
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="title"
                  label="Event Title"
                  rules={[{ required: true, message: 'Please enter event title' }]}
                >
                  <Input placeholder="Enter event title" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="type"
                  label="Event Type"
                  rules={[{ required: true, message: 'Please select event type' }]}
                >
                  <Select placeholder="Select event type">
                    {displayEventTypes.map((type) => (
                      <Option
                        key={type.id}
                        value={type.id}
                      >
                        <Badge
                          color={type.color}
                          text={type.name}
                        />
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="duration"
                  label="Duration (minutes)"
                  rules={[{ required: true, message: 'Please enter duration' }]}
                >
                  <Select placeholder="Select duration">
                    <Option value={15}>15 minutes</Option>
                    <Option value={30}>30 minutes</Option>
                    <Option value={60}>1 hour</Option>
                    <Option value={90}>1.5 hours</Option>
                    <Option value={120}>2 hours</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="date"
                  label="Date"
                  rules={[{ required: true, message: 'Please select date' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="time"
                  label="Time"
                  rules={[{ required: true, message: 'Please select time' }]}
                >
                  <TimePicker
                    style={{ width: '100%' }}
                    format="HH:mm"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="Description"
            >
              <TextArea
                rows={3}
                placeholder="Enter event description"
              />
            </Form.Item>

            <Form.Item
              name="isOnline"
              label="Meeting Type"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="Online"
                unCheckedChildren="In-person"
                onChange={(checked) => {
                  if (!checked) {
                    form.setFieldValue('createMeeting', false);
                  }
                }}
              />
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isOnline !== currentValues.isOnline
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isOnline') ? (
                  <Form.Item
                    name="meetingLink"
                    label="Meeting Link"
                  >
                    <Input placeholder="Enter meeting link (optional)" />
                  </Form.Item>
                ) : (
                  <Form.Item
                    name="location"
                    label="Location"
                  >
                    <Input placeholder="Enter location" />
                  </Form.Item>
                )
              }
            </Form.Item>

            {participants.length > 0 && (
              <Form.Item
                name="participants"
                label="Participants"
              >
                <Select
                  mode="multiple"
                  placeholder="Select participants"
                  options={participants.map((p) => ({ label: p.name, value: p.email }))}
                />
              </Form.Item>
            )}

            {/* Google Calendar Integration Options */}
            {isGoogleConnected && (
              <div className="bg-gray-50 p-4 rounded mb-4">
                <Title level={5}>Google Calendar Integration</Title>

                <Form.Item
                  name="syncToGoogle"
                  valuePropName="checked"
                >
                  <Switch
                    checkedChildren="Sync to Google"
                    unCheckedChildren="Local only"
                  />
                </Form.Item>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.syncToGoogle !== currentValues.syncToGoogle ||
                    prevValues.isOnline !== currentValues.isOnline
                  }
                >
                  {({ getFieldValue }) =>
                    getFieldValue('syncToGoogle') &&
                    getFieldValue('isOnline') && (
                      <Form.Item
                        name="createMeeting"
                        valuePropName="checked"
                      >
                        <Switch
                          checkedChildren="Create Google Meet"
                          unCheckedChildren="Regular event"
                        />
                      </Form.Item>
                    )
                  }
                </Form.Item>
              </div>
            )}

            <Form.Item className="mb-0">
              <Space className="w-full justify-end">
                <Button onClick={() => setModalVisible(false)}>Cancel</Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  {modalMode === 'add' ? 'Create Event' : 'Update Event'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default Calendar;
