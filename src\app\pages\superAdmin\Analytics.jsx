import React from 'react';
import { Card, Button, Space } from 'antd';
import { BarChartOutlined, ExportOutlined } from '@ant-design/icons';

const Analytics = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Analytics & Reports
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Platform insights and performance metrics
          </p>
        </div>
        <Space>
          <Button icon={<ExportOutlined />}>
            Export Report
          </Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <BarChartOutlined className="text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Analytics Dashboard Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will provide comprehensive analytics and reporting capabilities.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;
