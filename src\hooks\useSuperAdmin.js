import { useState, useCallback } from 'react';
import superAdminService from '@/services/superAdmin.service';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

/**
 * Custom hook for Super Admin operations
 * 
 * Provides methods and state management for super admin functionality
 * including user management, team operations, and audit logging.
 */
const useSuperAdmin = () => {
  const { hasPermission, isSuperAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Clear error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // ===== USER MANAGEMENT =====

  /**
   * Fetch users with filtering and pagination
   */
  const fetchUsers = useCallback(async (filters = {}) => {
    if (!hasPermission('users.view')) {
      setError('Insufficient permissions to view users');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.getUsers(filters);
      
      if (!result.success) {
        setError(result.error);
        showToast.error('Failed to fetch users');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch users';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Fetch pending user approvals
   */
  const fetchPendingApprovals = useCallback(async () => {
    if (!hasPermission('users.approve')) {
      setError('Insufficient permissions to view approvals');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.getPendingApprovals();
      
      if (!result.success) {
        setError(result.error);
        showToast.error('Failed to fetch pending approvals');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch pending approvals';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Approve a user
   */
  const approveUser = useCallback(async (approvalId, reviewNotes = '') => {
    if (!hasPermission('users.approve')) {
      setError('Insufficient permissions to approve users');
      showToast.error('Insufficient permissions to approve users');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.approveUser(approvalId, reviewNotes);
      
      if (result.success) {
        showToast.success('User approved successfully');
      } else {
        setError(result.error);
        showToast.error('Failed to approve user');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to approve user';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Reject a user
   */
  const rejectUser = useCallback(async (approvalId, reviewNotes = '') => {
    if (!hasPermission('users.reject')) {
      setError('Insufficient permissions to reject users');
      showToast.error('Insufficient permissions to reject users');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.rejectUser(approvalId, reviewNotes);
      
      if (result.success) {
        showToast.success('User rejected successfully');
      } else {
        setError(result.error);
        showToast.error('Failed to reject user');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to reject user';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  // ===== TEAM MANAGEMENT =====

  /**
   * Fetch teams with members
   */
  const fetchTeams = useCallback(async () => {
    if (!hasPermission('teams.view')) {
      setError('Insufficient permissions to view teams');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.getTeams();
      
      if (!result.success) {
        setError(result.error);
        showToast.error('Failed to fetch teams');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch teams';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Create a new team
   */
  const createTeam = useCallback(async (teamData) => {
    if (!hasPermission('teams.create')) {
      setError('Insufficient permissions to create teams');
      showToast.error('Insufficient permissions to create teams');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.createTeam(teamData);
      
      if (result.success) {
        showToast.success('Team created successfully');
      } else {
        setError(result.error);
        showToast.error('Failed to create team');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to create team';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Add member to team
   */
  const addTeamMember = useCallback(async (teamId, userId, roleInTeam = 'member') => {
    if (!hasPermission('teams.manage_members')) {
      setError('Insufficient permissions to manage team members');
      showToast.error('Insufficient permissions to manage team members');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.addTeamMember(teamId, userId, roleInTeam);
      
      if (result.success) {
        showToast.success('Team member added successfully');
      } else {
        setError(result.error);
        showToast.error('Failed to add team member');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to add team member';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  // ===== ANALYTICS =====

  /**
   * Fetch dashboard statistics
   */
  const fetchDashboardStats = useCallback(async () => {
    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.getDashboardStats();
      
      if (!result.success) {
        setError(result.error);
        showToast.error('Failed to fetch dashboard statistics');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch dashboard statistics';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [clearError]);

  // ===== AUDIT LOGS =====

  /**
   * Fetch audit logs
   */
  const fetchAuditLogs = useCallback(async (filters = {}) => {
    if (!hasPermission('system.audit')) {
      setError('Insufficient permissions to view audit logs');
      return { success: false, error: 'Insufficient permissions' };
    }

    setLoading(true);
    clearError();

    try {
      const result = await superAdminService.getAuditLogs(filters);
      
      if (!result.success) {
        setError(result.error);
        showToast.error('Failed to fetch audit logs');
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch audit logs';
      setError(errorMessage);
      showToast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [hasPermission, clearError]);

  /**
   * Log an audit event
   */
  const logAuditEvent = useCallback(async (eventData) => {
    try {
      const result = await superAdminService.logAuditEvent(eventData);
      return result;
    } catch (error) {
      console.error('Failed to log audit event:', error);
      return { success: false, error: error.message };
    }
  }, []);

  return {
    // State
    loading,
    error,
    
    // Utility methods
    clearError,
    
    // User management
    fetchUsers,
    fetchPendingApprovals,
    approveUser,
    rejectUser,
    
    // Team management
    fetchTeams,
    createTeam,
    addTeamMember,
    
    // Analytics
    fetchDashboardStats,
    
    // Audit logs
    fetchAuditLogs,
    logAuditEvent,
    
    // Permission helpers
    hasPermission,
    isSuperAdmin,
  };
};

export default useSuperAdmin;
