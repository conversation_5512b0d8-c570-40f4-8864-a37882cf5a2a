import { motion } from 'framer-motion';
import { Typography } from 'antd';

const { Text } = Typography;

const LoadingEffect = ({ 
  size = 'default', 
  message = 'Loading...', 
  showMessage = true,
  variant = 'dots',
  fullScreen = false 
}) => {
  const sizeConfig = {
    small: { container: 'w-8 h-8', dot: 'w-2 h-2', text: 'text-sm' },
    default: { container: 'w-12 h-12', dot: 'w-3 h-3', text: 'text-base' },
    large: { container: 'w-16 h-16', dot: 'w-4 h-4', text: 'text-lg' },
  };

  const config = sizeConfig[size];

  const containerClass = fullScreen 
    ? 'min-h-screen flex flex-col items-center justify-center bg-background'
    : 'flex flex-col items-center justify-center p-4';

  // Dots Loading Animation
  const DotsLoader = () => (
    <div className={`flex space-x-1 ${config.container}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`${config.dot} bg-primary rounded-full`}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  // Pulse Loading Animation
  const PulseLoader = () => (
    <motion.div
      className={`${config.container} bg-primary rounded-full`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.7, 1, 0.7],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );

  // Spinner Loading Animation
  const SpinnerLoader = () => (
    <motion.div
      className={`${config.container} border-4 border-gray-200 border-t-primary rounded-full`}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear',
      }}
    />
  );

  // Wave Loading Animation
  const WaveLoader = () => (
    <div className={`flex space-x-1 ${config.container}`}>
      {[0, 1, 2, 3, 4].map((index) => (
        <motion.div
          key={index}
          className={`w-1 bg-primary rounded-full`}
          animate={{
            height: ['20%', '100%', '20%'],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  // Orbit Loading Animation
  const OrbitLoader = () => (
    <div className={`relative ${config.container}`}>
      <motion.div
        className="absolute inset-0"
        animate={{ rotate: 360 }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        <div className={`${config.dot} bg-primary rounded-full absolute top-0 left-1/2 transform -translate-x-1/2`} />
      </motion.div>
      <motion.div
        className="absolute inset-0"
        animate={{ rotate: -360 }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        <div className={`${config.dot} bg-secondary rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2`} />
      </motion.div>
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'pulse':
        return <PulseLoader />;
      case 'spinner':
        return <SpinnerLoader />;
      case 'wave':
        return <WaveLoader />;
      case 'orbit':
        return <OrbitLoader />;
      default:
        return <DotsLoader />;
    }
  };

  return (
    <div className={containerClass}>
      {renderLoader()}
      
      {showMessage && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="mt-4 text-center"
        >
          <Text className={`text-text-secondary ${config.text}`}>
            {message}
          </Text>
        </motion.div>
      )}
    </div>
  );
};

// Preset loading components for common use cases
export const PageLoader = ({ message = 'Loading page...' }) => (
  <LoadingEffect 
    size="large" 
    message={message} 
    variant="dots" 
    fullScreen 
  />
);

export const ComponentLoader = ({ message = 'Loading...' }) => (
  <LoadingEffect 
    size="default" 
    message={message} 
    variant="spinner" 
  />
);

export const ButtonLoader = () => (
  <LoadingEffect 
    size="small" 
    showMessage={false} 
    variant="dots" 
  />
);

export const InlineLoader = ({ message = 'Loading...' }) => (
  <LoadingEffect 
    size="small" 
    message={message} 
    variant="pulse" 
  />
);

export default LoadingEffect;
