import { Suspense, lazy } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import PageLoader from '@/components/shared/PageLoader';

// Import the admin redirect component
const AdminRedirect = lazy(() => import('@/app/pages/superAdmin/AdminRedirect'));
import {
  publicRoutes,
  authRoutes,
  companyRoutes,
  interviewerRoutes,
  superAdminRoutes,
  errorRoutes,
} from './routes';
import MainLayout from '@/app/layouts/MainLayout';
import CompanyLayout from '@/app/layouts/CompanyLayout';
import InterviewerLayout from '@/app/layouts/InterviewerLayout';
import SuperAdminLayout from '@/app/layouts/SuperAdminLayout';
import ProtectedRoute from '@/router/ProtectedRoute';

// Suspense Wrapper Component
function SuspenseWrapper({ Component }) {
  return (
    <Suspense fallback={<PageLoader message={'Loading'} />}>
      <Component />
    </Suspense>
  );
}

// Helper to transform route definitions
const transformRoutes = (routes) => {
  return routes.map((route) => ({
    ...route,
    element: <SuspenseWrapper Component={route.component} />,
  }));
};

// Create the router configuration
const AppRouter = createBrowserRouter([
  // Public routes with MainLayout
  {
    path: '/',
    element: <MainLayout />,
    children: [
      // Home route (index)
      {
        index: true,
        element: (
          <SuspenseWrapper
            Component={publicRoutes[0].component}
            isPublicRoutes={true}
          />
        ),
      },
      // Other public routes
      ...transformRoutes(publicRoutes.slice(1)),
      // Public auth routes
      ...transformRoutes(authRoutes),
      // Error routes for public layout
      ...transformRoutes(errorRoutes),
    ],
  },

  // Company protected routes
  {
    path: '/org',
    element: <ProtectedRoute allowedRoles={['company']} />,
    children: [
      {
        element: <CompanyLayout />,
        children: [
          ...transformRoutes(companyRoutes),
          // Error routes for company layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },

  // Interviewer protected routes
  {
    path: '/sourcer',
    element: <ProtectedRoute allowedRoles={['interviewer']} />,
    children: [
      {
        element: <InterviewerLayout />,
        children: [
          ...transformRoutes(interviewerRoutes),
          // Error routes for interviewer layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },

  // Super Admin protected routes
  {
    path: '/admin',
    element: <ProtectedRoute allowedRoles={['super_admin']} />,
    children: [
      {
        element: <SuperAdminLayout />,
        children: [
          // Default redirect to dashboard
          {
            index: true,
            element: <SuspenseWrapper Component={AdminRedirect} />,
          },
          ...transformRoutes(superAdminRoutes),
          // Error routes for super admin layout
          ...transformRoutes(errorRoutes),
        ],
      },
    ],
  },
]);

export default AppRouter;
