/**
 * Calendar Page Component
 *
 * Universal calendar page that works for all user roles
 * with Google Calendar integration and Google Meet support
 */

import React, { useState, useEffect } from 'react';
import { Card, Typography, Alert } from 'antd';
import { Calendar as CalendarIcon } from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import Calendar from '@/components/shared/Calendar';
import useAuth from '@/hooks/useAuth';
import { INTERVIEWER_EVENT_TYPES } from '@/features/interviewer/constants';

const { Title, Text } = Typography;

const CalendarPage = () => {
  const { user, profile } = useAuth();
  const [participants, setParticipants] = useState([]);

  // Get role-specific event types
  const getRoleEventTypes = () => {
    if (!profile?.role) return [];

    switch (profile.role) {
      case 'candidate':
        return [
          { id: 'assessment', name: 'Assessment', color: 'blue' },
          { id: 'application_deadline', name: 'Application Deadline', color: 'red' },
          { id: 'follow_up', name: 'Follow Up', color: 'orange' },
          { id: 'meeting', name: 'Meeting', color: 'green' },
          { id: 'reminder', name: '<PERSON>minder', color: 'purple' },
        ];

      case 'company':
        return [
          { id: 'candidate_review', name: 'Candidate Review', color: 'blue' },
          { id: 'team_meeting', name: 'Team Meeting', color: 'green' },
          { id: 'hiring_deadline', name: 'Hiring Deadline', color: 'red' },
          { id: 'onboarding', name: 'Onboarding', color: 'cyan' },
          { id: 'strategy_session', name: 'Strategy Session', color: 'purple' },
        ];

      case 'interviewer':
        return INTERVIEWER_EVENT_TYPES;

      default:
        return [];
    }
  };

  // Get role-specific participants
  const getRoleParticipants = () => {
    // This would typically come from API calls based on user role
    // For now, return empty array - can be enhanced later
    return [];
  };

  useEffect(() => {
    // Load participants based on user role
    const roleParticipants = getRoleParticipants();
    setParticipants(roleParticipants);
  }, [profile?.role]);

  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <Alert
          message="Authentication Required"
          description="Please log in to access the calendar."
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className="calendar-page p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <LucideIcon
              icon={CalendarIcon}
              size={24}
              className="text-primary mr-3"
            />
            <div>
              <Title
                level={2}
                className="m-0"
              >
                Calendar
              </Title>
              <Text type="secondary">Manage your schedule and sync with Google Calendar</Text>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Component */}
      <Calendar
        userType={profile?.role || 'candidate'}
        eventTypes={getRoleEventTypes()}
        participants={participants}
        viewOptions={{
          month: true,
          day: true,
          agenda: true,
        }}
      />

      {/* Help Section */}
      <Card
        className="mt-6"
        size="small"
      >
        <Title level={5}>Calendar Features</Title>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <Text strong>Google Calendar Sync</Text>
            <div className="text-sm text-gray-600 mt-1">
              Connect your Google Calendar to sync events automatically
            </div>
          </div>
          <div>
            <Text strong>Google Meet Integration</Text>
            <div className="text-sm text-gray-600 mt-1">
              Create instant meetings or schedule them with events
            </div>
          </div>
          <div>
            <Text strong>Multi-Device Access</Text>
            <div className="text-sm text-gray-600 mt-1">
              Access your calendar from any device with real-time sync
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CalendarPage;
