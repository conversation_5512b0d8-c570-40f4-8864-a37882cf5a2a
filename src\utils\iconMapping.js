/**
 * Icon mapping from Ant Design to Lucide React
 * This file provides a centralized mapping for consistent icon usage
 */

import {
  // Navigation & Layout
  Home,
  Menu,
  X,
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  MoreHorizontal,
  
  // Actions & States
  Plus,
  Edit,
  Trash2,
  Save,
  Download,
  Upload,
  Copy,
  Share,
  Eye,
  EyeOff,
  Check,
  X as XIcon,
  AlertCircle,
  Info,
  HelpCircle,
  
  // Business & Work
  Building,
  Users,
  UserPlus,
  Calendar,
  Clock,
  Video,
  Phone,
  Mail,
  MapPin,
  Briefcase,
  DollarSign,
  TrendingUp,
  BarChart3,
  PieChart,
  
  // Technology
  Monitor,
  Smartphone,
  Globe,
  Wifi,
  Database,
  Server,
  Cloud,
  Shield,
  Lock,
  Unlock,
  
  // Media & Content
  FileText,
  File,
  Image,
  Play,
  Pause,
  Stop,
  Volume2,
  VolumeX,
  Camera,
  
  // UI Elements
  Star,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Bookmark,
  Tag,
  Filter,
  Sort,
  
  // Arrows & Directions
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  
  // Theme & Display
  Sun,
  Moon,
  Lightbulb,
  Palette,
  
  // Communication
  MessageCircle,
  Send,
  Inbox,
  AtSign,
  
  // Status & Feedback
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader,
  RefreshCw,
  
  // Special
  Gift,
  Award,
  Trophy,
  Target,
  Zap,
  Rocket,
  
} from 'lucide-react';

// Icon mapping object
export const iconMap = {
  // Navigation & Layout
  'HomeOutlined': Home,
  'MenuOutlined': Menu,
  'MenuFoldOutlined': Menu,
  'MenuUnfoldOutlined': Menu,
  'CloseOutlined': X,
  'SearchOutlined': Search,
  'BellOutlined': Bell,
  'UserOutlined': User,
  'SettingOutlined': Settings,
  'LogoutOutlined': LogOut,
  'LeftOutlined': ChevronLeft,
  'RightOutlined': ChevronRight,
  'UpOutlined': ChevronUp,
  'DownOutlined': ChevronDown,
  'EllipsisOutlined': MoreHorizontal,
  
  // Actions & States
  'PlusOutlined': Plus,
  'EditOutlined': Edit,
  'DeleteOutlined': Trash2,
  'SaveOutlined': Save,
  'DownloadOutlined': Download,
  'UploadOutlined': Upload,
  'CopyOutlined': Copy,
  'ShareAltOutlined': Share,
  'EyeOutlined': Eye,
  'EyeInvisibleOutlined': EyeOff,
  'CheckOutlined': Check,
  'CloseCircleOutlined': XCircle,
  'ExclamationCircleOutlined': AlertCircle,
  'InfoCircleOutlined': Info,
  'QuestionCircleOutlined': HelpCircle,
  
  // Business & Work
  'BankOutlined': Building,
  'TeamOutlined': Users,
  'UserAddOutlined': UserPlus,
  'CalendarOutlined': Calendar,
  'ClockCircleOutlined': Clock,
  'VideoCameraOutlined': Video,
  'PhoneOutlined': Phone,
  'MailOutlined': Mail,
  'EnvironmentOutlined': MapPin,
  'AppstoreOutlined': Briefcase,
  'DollarOutlined': DollarSign,
  'RiseOutlined': TrendingUp,
  'BarChartOutlined': BarChart3,
  'PieChartOutlined': PieChart,
  
  // Technology
  'LaptopOutlined': Monitor,
  'MobileOutlined': Smartphone,
  'GlobalOutlined': Globe,
  'WifiOutlined': Wifi,
  'DatabaseOutlined': Database,
  'CloudOutlined': Cloud,
  'SafetyOutlined': Shield,
  'LockOutlined': Lock,
  'UnlockOutlined': Unlock,
  
  // Media & Content
  'FileTextOutlined': FileText,
  'FileOutlined': File,
  'PictureOutlined': Image,
  'PlayCircleOutlined': Play,
  'PauseCircleOutlined': Pause,
  'StopOutlined': Stop,
  'SoundOutlined': Volume2,
  'CameraOutlined': Camera,
  
  // UI Elements
  'StarOutlined': Star,
  'HeartOutlined': Heart,
  'LikeOutlined': ThumbsUp,
  'DislikeOutlined': ThumbsDown,
  'FlagOutlined': Flag,
  'BookOutlined': Bookmark,
  'TagOutlined': Tag,
  'FilterOutlined': Filter,
  'SortAscendingOutlined': Sort,
  
  // Arrows & Directions
  'ArrowRightOutlined': ArrowRight,
  'ArrowLeftOutlined': ArrowLeft,
  'ArrowUpOutlined': ArrowUp,
  'ArrowDownOutlined': ArrowDown,
  'ExportOutlined': ExternalLink,
  
  // Theme & Display
  'SunOutlined': Sun,
  'BulbOutlined': Lightbulb,
  'BulbFilled': Lightbulb,
  
  // Communication
  'CommentOutlined': MessageCircle,
  'SendOutlined': Send,
  'InboxOutlined': Inbox,
  
  // Status & Feedback
  'CheckCircleOutlined': CheckCircle,
  'ExclamationTriangleOutlined': AlertTriangle,
  'LoadingOutlined': Loader,
  'SyncOutlined': RefreshCw,
  'ReloadOutlined': RefreshCw,
  
  // Special
  'GiftOutlined': Gift,
  'TrophyOutlined': Trophy,
  'ThunderboltOutlined': Zap,
  'RocketOutlined': Rocket,
  
  // Dashboard
  'DashboardOutlined': BarChart3,
  
  // Google specific
  'GoogleOutlined': Globe, // Using Globe as fallback for Google
  
  // Video/Meeting
  'DisconnectOutlined': X,
  
  // User management
  'UserSwitchOutlined': Users,
};

// Helper function to get Lucide icon from Ant Design icon name
export const getLucideIcon = (antIconName) => {
  return iconMap[antIconName] || HelpCircle; // Default fallback icon
};

// Export commonly used icons for direct import
export {
  Home,
  Menu,
  X,
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Calendar,
  Video,
  Users,
  Building,
  DollarSign,
  FileText,
  BarChart3,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Info,
  HelpCircle,
  Plus,
  Edit,
  Trash2,
  Eye,
  Globe,
  RefreshCw,
  Play,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
};
