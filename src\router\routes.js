import { lazy } from 'react';
import ProtectedRoute from './ProtectedRoute';

// Public Pages
const HomePage = lazy(() => import('@/app/pages/public/Home'));
const AboutPage = lazy(() => import('@/app/pages/public/About'));
const ContactPage = lazy(() => import('@/app/pages/public/Contact'));
const PricingPage = lazy(() => import('@/app/pages/public/Pricing'));
const ProductsPage = lazy(() => import('@/app/pages/public/Products'));
const BenefitsPage = lazy(() => import('@/app/pages/public/Benefits'));
const HowItWorksPage = lazy(() => import('@/app/pages/public/HowItWorks'));
const TestimonialsPage = lazy(() => import('@/app/pages/public/Testimonials'));
const FaqsPage = lazy(() => import('@/app/pages/public/Faqs'));
const HelpCenterPage = lazy(() => import('@/app/pages/public/HelpCenter'));
const TermsAndConditionsPage = lazy(() => import('@/app/pages/public/TermsAndConditions'));
const PrivacyPolicyPage = lazy(() => import('@/app/pages/public/PrivacyPolicy'));

// Auth Pages
const LoginPage = lazy(() => import('@/app/pages/auth/Login'));
const RegisterPage = lazy(() => import('@/app/pages/auth/Register'));
const GetStartedPage = lazy(() => import('@/app/pages/auth/GetStarted'));
// const ForgotPasswordPage = lazy(() => import('@/app/pages/auth/ForgotPassword'));
// const VerifyEmailPage = lazy(() => import('@/app/pages/auth/VerifyEmail'));
const AuthCallback = lazy(() => import('@/app/pages/auth/AuthCallback'));

// Error Pages
const NotFound = lazy(() => import('@/app/pages/error/NotFound'));
const AccessDenied = lazy(() => import('@/app/pages/error/AccessDenied'));

// Company Dashboard Pages
const CompanyDashboard = lazy(() => import('@/app/pages/protected/company/Dashboard'));
const CompanyBilling = lazy(() => import('../app/pages/protected/company/Billing'));
const CompanyCalendar = lazy(() => import('@/features/company/pages/CompanyCalendar'));
const CompanyCandidates = lazy(() => import('../app/pages/protected/company/Candidates'));
const CompanyInterviewReports = lazy(
  () => import('../app/pages/protected/company/InterviewReports')
);
const CompanyProfile = lazy(() => import('../app/pages/protected/company/Profile'));
const CompanyInterviewerInfo = lazy(() => import('../app/pages/protected/company/InterviewerInfo'));
const CompanySetting = lazy(() => import('../app/pages/protected/company/Setting'));

// Interviewer Dashboard Pages
const InterviewerDashboard = lazy(() => import('@/app/pages/protected/interviewer/Dashboard'));
const InterviewerBilling = lazy(() => import('@/app/pages/protected/interviewer/Billing'));
const InterviewerCalendar = lazy(() => import('@/app/pages/protected/interviewer/Calendar'));
const InterviewerCandidates = lazy(() => import('@/app/pages/protected/interviewer/Candidates'));
const InterviewerEarnings = lazy(() => import('@/app/pages/protected/interviewer/Earnings'));
const InterviewerProfile = lazy(() => import('@/app/pages/protected/interviewer/Profile'));
const InterviewerSupport = lazy(() => import('@/app/pages/protected/interviewer/Support'));
const InterviewerSetting = lazy(() => import('@/app/pages/protected/interviewer/Setting'));
const InterviewerInterviews = lazy(() => import('@/app/pages/protected/interviewer/Interviews'));
const InterviewerInterviewReports = lazy(
  () => import('../app/pages/protected/interviewer/interviews/InterviewReports')
);

// Public routes
export const publicRoutes = [
  { index: true, component: HomePage },
  { path: 'get-started', component: GetStartedPage },
  { path: 'about', component: AboutPage },
  { path: 'contact', component: ContactPage },
  { path: 'pricing', component: PricingPage },
  { path: 'products', component: ProductsPage },
  { path: 'benefits', component: BenefitsPage },
  { path: 'how-it-works', component: HowItWorksPage },
  { path: 'testimonials', component: TestimonialsPage },
  { path: 'faqs', component: FaqsPage },
  { path: 'help-center', component: HelpCenterPage },
  { path: 'terms', component: TermsAndConditionsPage },
  { path: 'privacy', component: PrivacyPolicyPage },
];

// Auth routes
export const authRoutes = [
  { path: 'login', component: LoginPage },
  { path: 'register', component: RegisterPage },
  { path: 'callback', component: AuthCallback },
];

// Company routes
export const companyRoutes = [
  { path: 'dashboard', component: CompanyDashboard },
  { path: 'billing', component: CompanyBilling },
  { path: 'calendar', component: CompanyCalendar },
  { path: 'candidates', component: CompanyCandidates },
  { path: 'interviewReports', component: CompanyInterviewReports },
  { path: 'profile', component: CompanyProfile },
  { path: 'interviewerInfo', component: CompanyInterviewerInfo },
  { path: 'setting', component: CompanySetting },
];

// Interviewer routes
export const interviewerRoutes = [
  { path: 'dashboard', component: InterviewerDashboard },
  { path: 'billing', component: InterviewerBilling },
  { path: 'calendar', component: InterviewerCalendar },
  { path: 'candidates', component: InterviewerCandidates },
  { path: 'earnings', component: InterviewerEarnings },
  { path: 'interviews', component: InterviewerInterviews },
  { path: 'interview-reports', component: InterviewerInterviewReports },
  { path: 'profile', component: InterviewerProfile },
  { path: 'support', component: InterviewerSupport },
  { path: 'setting', component: InterviewerSetting },
];

// Super Admin routes
export const superAdminRoutes = [
  { path: 'dashboard', component: lazy(() => import('@/app/pages/superAdmin/Dashboard')) },
  { path: 'users', component: lazy(() => import('@/app/pages/superAdmin/UserManagement')) },
  { path: 'teams', component: lazy(() => import('@/app/pages/superAdmin/TeamManagement')) },
  {
    path: 'interviews',
    component: lazy(() => import('@/app/pages/superAdmin/InterviewManagement')),
  },
  { path: 'analytics', component: lazy(() => import('@/app/pages/superAdmin/Analytics')) },
  { path: 'audit', component: lazy(() => import('@/app/pages/superAdmin/AuditLogs')) },
  { path: 'support', component: lazy(() => import('@/app/pages/superAdmin/SupportManagement')) },
  // { path: 'settings', component: lazy(() => import('@/app/pages/superAdmin/SystemSettings')) },
  // { path: 'profile', component: lazy(() => import('@/app/pages/superAdmin/Profile')) },
];

// Error routes
export const errorRoutes = [
  { path: 'access-denied', component: AccessDenied },
  { path: '*', component: NotFound },
];

// Route guards
export const guards = {
  ProtectedRoute,
};
