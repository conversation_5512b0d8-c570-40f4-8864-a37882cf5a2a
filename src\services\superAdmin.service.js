import { supabase } from '@/utils/supabaseClient';

/**
 * Super Admin Service
 * 
 * Handles all super admin related operations including:
 * - User management and approvals
 * - Team management
 * - Permission management
 * - Audit logging
 * - System analytics
 */

class SuperAdminService {
  // ===== USER MANAGEMENT =====
  
  /**
   * Get all users with filtering and pagination
   */
  async getUsers(filters = {}) {
    try {
      let query = supabase
        .from('profiles')
        .select(`
          *,
          company_profiles(*),
          interviewer_profiles(*),
          super_admin_profiles(*)
        `);

      // Apply filters
      if (filters.role && filters.role !== 'all') {
        query = query.eq('role', filters.role);
      }

      if (filters.search) {
        query = query.or(`
          full_name.ilike.%${filters.search}%,
          email.ilike.%${filters.search}%
        `);
      }

      // Apply pagination
      if (filters.page && filters.pageSize) {
        const from = (filters.page - 1) * filters.pageSize;
        const to = from + filters.pageSize - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return { success: true, data, count };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get pending user approvals
   */
  async getPendingApprovals() {
    try {
      const { data, error } = await supabase
        .from('user_approvals')
        .select(`
          *,
          profiles(*)
        `)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Approve user registration or profile update
   */
  async approveUser(approvalId, reviewNotes = '') {
    try {
      const { data, error } = await supabase
        .from('user_approvals')
        .update({
          status: 'approved',
          review_notes: reviewNotes,
          approved_at: new Date().toISOString(),
        })
        .eq('id', approvalId)
        .select()
        .single();

      if (error) throw error;

      // Log the approval action
      await this.logAuditEvent({
        action: 'user_approved',
        resource_type: 'user_approval',
        resource_id: approvalId,
        new_values: { status: 'approved', review_notes: reviewNotes },
      });

      return { success: true, data };
    } catch (error) {
      console.error('Error approving user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Reject user registration or profile update
   */
  async rejectUser(approvalId, reviewNotes = '') {
    try {
      const { data, error } = await supabase
        .from('user_approvals')
        .update({
          status: 'rejected',
          review_notes: reviewNotes,
          rejected_at: new Date().toISOString(),
        })
        .eq('id', approvalId)
        .select()
        .single();

      if (error) throw error;

      // Log the rejection action
      await this.logAuditEvent({
        action: 'user_rejected',
        resource_type: 'user_approval',
        resource_id: approvalId,
        new_values: { status: 'rejected', review_notes: reviewNotes },
      });

      return { success: true, data };
    } catch (error) {
      console.error('Error rejecting user:', error);
      return { success: false, error: error.message };
    }
  }

  // ===== TEAM MANAGEMENT =====

  /**
   * Get all teams with members
   */
  async getTeams() {
    try {
      const { data, error } = await supabase
        .from('teams')
        .select(`
          *,
          team_members(
            *,
            profiles(*)
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching teams:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new team
   */
  async createTeam(teamData) {
    try {
      const { data, error } = await supabase
        .from('teams')
        .insert(teamData)
        .select()
        .single();

      if (error) throw error;

      // Log the team creation
      await this.logAuditEvent({
        action: 'team_created',
        resource_type: 'team',
        resource_id: data.id,
        new_values: teamData,
      });

      return { success: true, data };
    } catch (error) {
      console.error('Error creating team:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add member to team
   */
  async addTeamMember(teamId, userId, roleInTeam = 'member') {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .insert({
          team_id: teamId,
          user_id: userId,
          role_in_team: roleInTeam,
        })
        .select()
        .single();

      if (error) throw error;

      // Log the team member addition
      await this.logAuditEvent({
        action: 'team_member_added',
        resource_type: 'team_member',
        resource_id: data.id,
        new_values: { team_id: teamId, user_id: userId, role_in_team: roleInTeam },
      });

      return { success: true, data };
    } catch (error) {
      console.error('Error adding team member:', error);
      return { success: false, error: error.message };
    }
  }

  // ===== ANALYTICS =====

  /**
   * Get dashboard statistics
   */
  async getDashboardStats() {
    try {
      // Get user counts
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      const { count: pendingApprovals } = await supabase
        .from('user_approvals')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      const { count: activeInterviews } = await supabase
        .from('interviews')
        .select('*', { count: 'exact', head: true })
        .in('status', ['scheduled', 'in_progress']);

      // Get recent activities
      const { data: recentActivities } = await supabase
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      return {
        success: true,
        data: {
          totalUsers: totalUsers || 0,
          pendingApprovals: pendingApprovals || 0,
          activeInterviews: activeInterviews || 0,
          recentActivities: recentActivities || [],
        },
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return { success: false, error: error.message };
    }
  }

  // ===== AUDIT LOGGING =====

  /**
   * Log an audit event
   */
  async logAuditEvent(eventData) {
    try {
      const { data, error } = await supabase
        .from('audit_logs')
        .insert({
          ...eventData,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error logging audit event:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getAuditLogs(filters = {}) {
    try {
      let query = supabase
        .from('audit_logs')
        .select(`
          *,
          profiles(full_name, email)
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.action) {
        query = query.eq('action', filters.action);
      }

      if (filters.resource_type) {
        query = query.eq('resource_type', filters.resource_type);
      }

      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }

      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }

      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      // Apply pagination
      if (filters.page && filters.pageSize) {
        const from = (filters.page - 1) * filters.pageSize;
        const to = from + filters.pageSize - 1;
        query = query.range(from, to);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return { success: true, data, count };
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      return { success: false, error: error.message };
    }
  }

  // ===== PERMISSIONS =====

  /**
   * Check if user has specific permission
   */
  async checkPermission(userId, permissionName) {
    try {
      const { data, error } = await supabase
        .rpc('check_user_permission', {
          user_id_param: userId,
          permission_name_param: permissionName,
        });

      if (error) throw error;

      return { success: true, hasPermission: data };
    } catch (error) {
      console.error('Error checking permission:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId) {
    try {
      const { data, error } = await supabase
        .rpc('get_user_permissions', {
          user_id_param: userId,
        });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
const superAdminService = new SuperAdminService();
export default superAdminService;
