import React from 'react';
import { Card, Descriptions, Tag } from 'antd';
import useAuth from '@/hooks/useAuth';

const RouteDebug = () => {
  const { 
    user, 
    role, 
    isAuthenticated, 
    isInitialized, 
    loading, 
    isSuperAdmin, 
    adminLevel,
    permissions 
  } = useAuth();

  return (
    <div className="p-4">
      <Card title="Route Debug Information" className="mb-4">
        <Descriptions column={1} bordered>
          <Descriptions.Item label="User ID">
            {user?.id || 'Not available'}
          </Descriptions.Item>
          <Descriptions.Item label="Email">
            {user?.email || 'Not available'}
          </Descriptions.Item>
          <Descriptions.Item label="Role">
            <Tag color={role === 'super_admin' ? 'gold' : 'blue'}>
              {role || 'Not set'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Admin Level">
            <Tag color="purple">
              {adminLevel || 'Not set'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Is Authenticated">
            <Tag color={isAuthenticated ? 'green' : 'red'}>
              {isAuthenticated ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Is Initialized">
            <Tag color={isInitialized ? 'green' : 'red'}>
              {isInitialized ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Loading">
            <Tag color={loading ? 'orange' : 'green'}>
              {loading ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Is Super Admin">
            <Tag color={isSuperAdmin() ? 'gold' : 'gray'}>
              {isSuperAdmin() ? 'Yes' : 'No'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Permissions Count">
            {permissions?.length || 0}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      
      {permissions && permissions.length > 0 && (
        <Card title="User Permissions" size="small">
          <div className="flex flex-wrap gap-1">
            {permissions.map((perm, index) => (
              <Tag key={index} size="small">
                {perm.permission_name}
              </Tag>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default RouteDebug;
