import { Navigate, Outlet } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import PageLoader from '@/components/shared/PageLoader';

const ProtectedRoute = ({ allowedRoles = [] }) => {
  const { user, role, loading, isAuthenticated, isInitialized } = useAuth();

  // Show loading while authentication is in progress OR not yet initialized
  if (loading || !isInitialized) {
    return <PageLoader message={'Preparing your dashboard'} />;
  }

  // Check if user is authenticated (only after initialization is complete)
  if (!user || !isAuthenticated) {
    return (
      <Navigate
        to="/login"
        replace
      />
    );
  }

  // Check role permissions
  if (allowedRoles.length > 0 && role && !allowedRoles.includes(role)) {
    return (
      <Navigate
        to="/access-denied"
        replace
      />
    );
  }

  return <Outlet />;
};

export default ProtectedRoute;
