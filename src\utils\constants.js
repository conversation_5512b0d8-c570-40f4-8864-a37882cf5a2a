/**
 * Global constants for the interview management platform
 */
import {
  Home,
  Info,
  Phone,
  Briefcase,
  HelpCircle,
  User,
  UserPlus,
  BarChart3,
  MessageCircle,
  Gift,
  DollarSign,
  Play,
} from 'lucide-react';

// Role Constants
export const ROLES = {
  COMPANY: 'company',
  INTERVIEWER: 'interviewer',
};

// Assessment Score Ranges
export const SCORE_LEVELS = {
  LOW: 0,
  MEDIUM: 50,
  HIGH: 80,
};

// Date/Time Formats
export const DATE_FORMAT = 'DD-MM-YYYY';
export const TIME_FORMAT = 'hh:mm A';

// Pagination
export const PAGE_SIZE = 10;

// Main navigation items
export const MAIN_NAV_ITEMS = [
  {
    key: '/',
    label: 'Home',
    icon: Home,
    description: 'Back to homepage',
  },
  {
    key: '/products',
    label: 'Products',
    icon: Briefcase,
    description: 'Explore our products',
  },
  {
    key: '/benefits',
    label: 'Benefits',
    icon: Gift,
    description: 'See how we can help you',
  },
  {
    key: '/pricing',
    label: 'Pricing',
    icon: DollarSign,
    description: 'View our pricing plans',
  },
];

// Footer navigation items (moved from header "More" dropdown)
export const FOOTER_NAV_ITEMS = [
  {
    key: 'about',
    label: 'About',
    icon: Info,
    path: '/about',
  },
  {
    key: 'contact',
    label: 'Contact',
    icon: Phone,
    path: '/contact',
  },
  {
    key: 'testimonials',
    label: 'Testimonials',
    icon: MessageCircle,
    path: '/testimonials',
  },
  {
    key: 'faqs',
    label: 'FAQs',
    icon: HelpCircle,
    path: '/faqs',
  },
  {
    key: 'how-it-works',
    label: 'How It Works',
    icon: Play,
    path: '/how-it-works',
  },
  {
    key: 'help-center',
    label: 'Help Center',
    icon: HelpCircle,
    path: '/help-center',
  },
];

// Authentication navigation items
export const AUTH_NAV_ITEMS = {
  unauthenticated: [
    {
      key: 'login',
      label: 'Login',
      icon: User,
      description: 'Sign in to your account',
    },
    {
      key: 'get-started',
      label: 'Get Started',
      icon: UserPlus,
      description: 'Start your journey with us',
    },
  ],
  authenticated: {
    key: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'Go to your dashboard',
  },
};

// Get dashboard path based on user role
export const getDashboardPath = (role) => {
  if (role === 'company') return '/org/dashboard';
  if (role === 'interviewer') return '/sourcer/dashboard';
  return '/'; // Unified dashboard path
};

// Import role-specific constants for convenience
export * from '@/features/company/constants';
export * from '@/features/interviewer/constants';
