import { UserButton } from '@clerk/clerk-react';
import { useColorModeStore } from '@/store/colorMode.store';

const StyledUserButton = ({ size = 'default', showName = false }) => {
  const { colorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  const getClerkUserButtonTheme = () => {
    return {
      elements: {
        // Root container
        rootBox: {
          width: 'auto',
          height: 'auto',
        },
        
        // Avatar button
        avatarBox: {
          width: size === 'small' ? '32px' : size === 'large' ? '48px' : '40px',
          height: size === 'small' ? '32px' : size === 'large' ? '48px' : '40px',
          borderRadius: '50%',
          border: `2px solid ${isDark ? '#404040' : '#e8e8e8'}`,
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            borderColor: '#1890ff',
            transform: 'scale(1.05)',
            boxShadow: isDark 
              ? '0 4px 12px rgba(24, 144, 255, 0.3)' 
              : '0 4px 12px rgba(24, 144, 255, 0.2)',
          },
        },

        // User preview (when dropdown is open)
        userPreview: {
          backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
          border: `1px solid ${isDark ? '#404040' : '#e8e8e8'}`,
          borderRadius: '12px',
          padding: '16px',
          boxShadow: isDark 
            ? '0 8px 24px rgba(0, 0, 0, 0.4)' 
            : '0 8px 24px rgba(0, 0, 0, 0.1)',
        },

        // User preview main identifier
        userPreviewMainIdentifier: {
          color: isDark ? '#ffffff' : '#000000',
          fontSize: '16px',
          fontWeight: '600',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        },

        // User preview secondary identifier
        userPreviewSecondaryIdentifier: {
          color: isDark ? '#a6a6a6' : '#666666',
          fontSize: '14px',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        },

        // Dropdown menu
        card: {
          backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
          border: `1px solid ${isDark ? '#404040' : '#e8e8e8'}`,
          borderRadius: '12px',
          boxShadow: isDark 
            ? '0 8px 24px rgba(0, 0, 0, 0.4)' 
            : '0 8px 24px rgba(0, 0, 0, 0.1)',
          padding: '8px',
          minWidth: '240px',
        },

        // Menu items
        menuItem: {
          borderRadius: '8px',
          padding: '12px 16px',
          margin: '2px 0',
          color: isDark ? '#ffffff' : '#000000',
          fontSize: '14px',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5',
            color: '#1890ff',
          },
        },

        // Menu list
        menuList: {
          padding: '0',
          margin: '0',
        },

        // Divider
        divider: {
          backgroundColor: isDark ? '#404040' : '#e8e8e8',
          margin: '8px 0',
          height: '1px',
        },

        // Action buttons
        menuButton: {
          backgroundColor: 'transparent',
          border: 'none',
          borderRadius: '8px',
          padding: '12px 16px',
          margin: '2px 0',
          color: isDark ? '#ffffff' : '#000000',
          fontSize: '14px',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          cursor: 'pointer',
          width: '100%',
          textAlign: 'left',
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: isDark ? '#2a2a2a' : '#f5f5f5',
            color: '#1890ff',
          },
        },

        // Sign out button
        menuButtonPrimary: {
          backgroundColor: '#ff4d4f',
          border: 'none',
          borderRadius: '8px',
          padding: '12px 16px',
          margin: '8px 0 0 0',
          color: '#ffffff',
          fontSize: '14px',
          fontWeight: '500',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          cursor: 'pointer',
          width: '100%',
          textAlign: 'center',
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: '#ff7875',
            transform: 'translateY(-1px)',
          },
        },

        // Footer
        footer: {
          padding: '12px 16px 8px',
          borderTop: `1px solid ${isDark ? '#404040' : '#e8e8e8'}`,
          marginTop: '8px',
        },

        // Footer text
        footerText: {
          color: isDark ? '#8c8c8c' : '#8c8c8c',
          fontSize: '12px',
          textAlign: 'center',
          fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        },

        // Badge (for notifications, etc.)
        badge: {
          backgroundColor: '#1890ff',
          color: '#ffffff',
          fontSize: '11px',
          fontWeight: '600',
          borderRadius: '10px',
          padding: '2px 6px',
          minWidth: '18px',
          height: '18px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
      },
      variables: {
        colorPrimary: '#1890ff',
        colorDanger: '#ff4d4f',
        colorSuccess: '#52c41a',
        colorWarning: '#faad14',
        colorNeutral: isDark ? '#ffffff' : '#000000',
        colorBackground: isDark ? '#1f1f1f' : '#ffffff',
        colorInputBackground: isDark ? '#262626' : '#ffffff',
        colorText: isDark ? '#ffffff' : '#000000',
        colorTextSecondary: isDark ? '#a6a6a6' : '#666666',
        borderRadius: '12px',
        fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      },
    };
  };

  return (
    <div className="flex items-center">
      <UserButton
        appearance={getClerkUserButtonTheme()}
        showName={showName}
        afterSignOutUrl="/"
      />
    </div>
  );
};

export default StyledUserButton;
