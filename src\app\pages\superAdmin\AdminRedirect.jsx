import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import PageLoader from '@/components/shared/PageLoader';

const AdminRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to dashboard immediately
    navigate('/admin/dashboard', { replace: true });
  }, [navigate]);

  return <PageLoader message="Redirecting to dashboard..." />;
};

export default AdminRedirect;
