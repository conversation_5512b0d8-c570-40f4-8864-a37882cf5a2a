import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  Tag,
  Button,
  Space,
  Tooltip,
  Spin,
} from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CalendarOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Users,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Shield,
  BarChart3,
  MessageSquare,
} from 'lucide-react';
import { Link } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import useSuperAdmin from '@/hooks/useSuperAdmin';

const SuperAdminDashboard = () => {
  const { hasPermission, isSuperAdmin, teamMemberships } = useAuth();
  const { fetchDashboardStats, fetchPendingApprovals, loading, error } = useSuperAdmin();

  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalUsers: 0,
      pendingApprovals: 0,
      activeInterviews: 0,
      monthlyRevenue: 0,
      systemHealth: 98.5,
    },
    recentActivities: [],
    pendingApprovals: [],
    systemAlerts: [],
    teamStats: [],
  });
  const [refreshing, setRefreshing] = useState(false);

  // Load dashboard data
  const loadDashboardData = async () => {
    setRefreshing(true);
    try {
      // Fetch real stats
      const statsResult = await fetchDashboardStats();
      if (statsResult.success) {
        setDashboardData((prev) => ({
          ...prev,
          stats: {
            ...prev.stats,
            totalUsers: statsResult.data.totalUsers,
            pendingApprovals: statsResult.data.pendingApprovals,
            activeInterviews: statsResult.data.activeInterviews,
          },
          recentActivities: statsResult.data.recentActivities,
        }));
      }

      // Fetch pending approvals
      const approvalsResult = await fetchPendingApprovals();
      if (approvalsResult.success) {
        setDashboardData((prev) => ({
          ...prev,
          pendingApprovals: approvalsResult.data.slice(0, 5), // Show only first 5
        }));
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadDashboardData();

    // Set up mock data for features not yet implemented
    setDashboardData((prev) => ({
      ...prev,
      systemAlerts: [
        {
          id: 1,
          type: 'warning',
          title: 'High Server Load',
          description: 'Server CPU usage at 85%',
          timestamp: '5 minutes ago',
        },
        {
          id: 2,
          type: 'info',
          title: 'Scheduled Maintenance',
          description: 'System maintenance scheduled for Sunday 2:00 AM',
          timestamp: '1 hour ago',
        },
        {
          id: 3,
          type: 'error',
          title: 'Failed Email Delivery',
          description: '3 notification emails failed to send',
          timestamp: '2 hours ago',
        },
      ],
      teamStats: [
        { team: 'Approvers', members: 5, activeToday: 3, tasksCompleted: 12 },
        { team: 'Monitors', members: 8, activeToday: 6, tasksCompleted: 28 },
        { team: 'Support Agents', members: 12, activeToday: 9, tasksCompleted: 45 },
        { team: 'Analysts', members: 4, activeToday: 2, tasksCompleted: 8 },
      ],
    }));
  }, [fetchDashboardStats, fetchPendingApprovals]);

  const handleRefresh = () => {
    loadDashboardData();
  };

  const activityColumns = [
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (text) => <span className="font-medium">{text}</span>,
    },
    {
      title: 'Details',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text) => <span className="text-gray-500">{text}</span>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'completed' ? 'green' : status === 'pending' ? 'orange' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
  ];

  const approvalColumns = [
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="font-medium">{text}</span>,
    },
    {
      title: 'Submitted By',
      dataIndex: 'submittedBy',
      key: 'submittedBy',
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priority === 'high' ? 'red' : priority === 'normal' ? 'orange' : 'green'}>
          {priority.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            type="primary"
          >
            Review
          </Button>
          <Button size="small">
            <EyeOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const teamColumns = [
    {
      title: 'Team',
      dataIndex: 'team',
      key: 'team',
      render: (text) => <span className="font-medium">{text}</span>,
    },
    {
      title: 'Members',
      dataIndex: 'members',
      key: 'members',
    },
    {
      title: 'Active Today',
      dataIndex: 'activeToday',
      key: 'activeToday',
      render: (active, record) => (
        <span className={active > record.members * 0.7 ? 'text-green-600' : 'text-orange-600'}>
          {active}/{record.members}
        </span>
      ),
    },
    {
      title: 'Tasks Completed',
      dataIndex: 'tasksCompleted',
      key: 'tasksCompleted',
      render: (tasks) => <span className="font-medium">{tasks}</span>,
    },
  ];

  const quickActions = [
    {
      title: 'User Management',
      description: 'Manage users and approvals',
      icon: <Users className="w-6 h-6" />,
      link: '/admin/users',
      permission: 'users.view',
    },
    {
      title: 'Team Management',
      description: 'Organize teams and roles',
      icon: <Shield className="w-6 h-6" />,
      link: '/admin/teams',
      permission: 'teams.view',
    },
    {
      title: 'Analytics',
      description: 'View reports and insights',
      icon: <BarChart3 className="w-6 h-6" />,
      link: '/admin/analytics',
      permission: 'analytics.view',
    },
    {
      title: 'Support Center',
      description: 'Handle support requests',
      icon: <MessageSquare className="w-6 h-6" />,
      link: '/admin/support',
      permission: 'support.tickets',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Super Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Platform overview and management center
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            icon={<ReloadOutlined />}
            loading={refreshing}
            onClick={handleRefresh}
          >
            Refresh
          </Button>
          <Button icon={<SettingOutlined />}>System Settings</Button>
          <Button
            type="primary"
            icon={<Activity className="w-4 h-4" />}
          >
            View All Activities
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <Row gutter={[16, 16]}>
        <Col
          xs={24}
          sm={12}
          lg={6}
        >
          <Card>
            <Statistic
              title="Total Users"
              value={dashboardData.stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          lg={6}
        >
          <Card>
            <Statistic
              title="Pending Approvals"
              value={dashboardData.stats.pendingApprovals}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          lg={6}
        >
          <Card>
            <Statistic
              title="Active Interviews"
              value={dashboardData.stats.activeInterviews}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          lg={6}
        >
          <Card>
            <Statistic
              title="System Health"
              value={dashboardData.stats.systemHealth}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
            <Progress
              percent={dashboardData.stats.systemHealth}
              showInfo={false}
              strokeColor="#52c41a"
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card
        title="Quick Actions"
        className="mb-6"
      >
        <Row gutter={[16, 16]}>
          {quickActions.map(
            (action, index) =>
              hasPermission(action.permission) && (
                <Col
                  xs={24}
                  sm={12}
                  lg={6}
                  key={index}
                >
                  <Link to={action.link}>
                    <Card
                      hoverable
                      className="text-center h-full"
                      bodyStyle={{ padding: '20px' }}
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className="p-3 bg-blue-50 dark:bg-blue-900 rounded-full">
                          {action.icon}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {action.title}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </Card>
                  </Link>
                </Col>
              )
          )}
        </Row>
      </Card>

      {/* Main Content Grid */}
      <Row gutter={[16, 16]}>
        {/* Recent Activities */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title="Recent Activities"
            extra={<Link to="/admin/audit">View All</Link>}
          >
            <Table
              dataSource={dashboardData.recentActivities}
              columns={activityColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* Pending Approvals */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title="Pending Approvals"
            extra={<Link to="/admin/users">Manage All</Link>}
          >
            <Table
              dataSource={dashboardData.pendingApprovals}
              columns={approvalColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* Team Performance */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title="Team Performance"
            extra={<Link to="/admin/teams">Manage Teams</Link>}
          >
            <Table
              dataSource={dashboardData.teamStats}
              columns={teamColumns}
              pagination={false}
              size="small"
              rowKey="team"
            />
          </Card>
        </Col>

        {/* System Alerts */}
        <Col
          xs={24}
          lg={12}
        >
          <Card title="System Alerts">
            <div className="space-y-3">
              {dashboardData.systemAlerts.map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800"
                >
                  <div className="flex-shrink-0">
                    {alert.type === 'error' && <AlertTriangle className="w-5 h-5 text-red-500" />}
                    {alert.type === 'warning' && <WarningOutlined className="text-orange-500" />}
                    {alert.type === 'info' && <CheckCircle className="w-5 h-5 text-blue-500" />}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">{alert.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{alert.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SuperAdminDashboard;
