import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message,
  Tooltip,
  Badge,
  Dropdown,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Users, UserCheck, UserX, Clock, Filter } from 'lucide-react';
import useAuth from '@/hooks/useAuth';

const { Search } = Input;
const { Option } = Select;

const UserManagement = () => {
  const { hasPermission } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [filters, setFilters] = useState({
    role: 'all',
    status: 'all',
    search: '',
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [form] = Form.useForm();

  // Mock data - replace with actual API calls
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setUsers([
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'company',
          status: 'pending',
          company: 'TechCorp Solutions',
          joinDate: '2024-01-15',
          lastLogin: '2024-01-14 10:30',
          profileCompletion: 85,
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'interviewer',
          status: 'active',
          company: 'Freelancer',
          joinDate: '2024-01-10',
          lastLogin: '2024-01-15 09:15',
          profileCompletion: 95,
        },
        {
          id: '3',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          role: 'company',
          status: 'active',
          company: 'Global Innovations Inc',
          joinDate: '2024-01-08',
          lastLogin: '2024-01-15 14:22',
          profileCompletion: 78,
        },
        {
          id: '4',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          role: 'interviewer',
          status: 'suspended',
          company: 'Freelancer',
          joinDate: '2024-01-05',
          lastLogin: '2024-01-12 16:45',
          profileCompletion: 92,
        },
        {
          id: '5',
          name: 'Alex Rodriguez',
          email: '<EMAIL>',
          role: 'interviewer',
          status: 'pending',
          company: 'Freelancer',
          joinDate: '2024-01-15',
          lastLogin: 'Never',
          profileCompletion: 60,
        },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleApprove = (userId) => {
    Modal.confirm({
      title: 'Approve User',
      content: 'Are you sure you want to approve this user?',
      onOk: () => {
        setUsers(users.map(user => 
          user.id === userId ? { ...user, status: 'active' } : user
        ));
        message.success('User approved successfully');
      },
    });
  };

  const handleReject = (userId) => {
    Modal.confirm({
      title: 'Reject User',
      content: 'Are you sure you want to reject this user? This action cannot be undone.',
      onOk: () => {
        setUsers(users.filter(user => user.id !== userId));
        message.success('User rejected successfully');
      },
    });
  };

  const handleSuspend = (userId) => {
    Modal.confirm({
      title: 'Suspend User',
      content: 'Are you sure you want to suspend this user?',
      onOk: () => {
        setUsers(users.map(user => 
          user.id === userId ? { ...user, status: 'suspended' } : user
        ));
        message.success('User suspended successfully');
      },
    });
  };

  const handleViewDetails = (user) => {
    setSelectedUser(user);
    setModalVisible(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'pending': return 'orange';
      case 'suspended': return 'red';
      default: return 'default';
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'company': return 'blue';
      case 'interviewer': return 'purple';
      case 'super_admin': return 'gold';
      default: return 'default';
    }
  };

  const userMenuItems = (user) => [
    {
      key: 'view',
      icon: <EyeOutlined />,
      label: 'View Details',
      onClick: () => handleViewDetails(user),
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: 'Edit Profile',
      disabled: !hasPermission('users.update'),
    },
    ...(user.status === 'pending' ? [
      {
        key: 'approve',
        icon: <CheckCircleOutlined />,
        label: 'Approve',
        onClick: () => handleApprove(user.id),
        disabled: !hasPermission('users.approve'),
      },
      {
        key: 'reject',
        icon: <CloseCircleOutlined />,
        label: 'Reject',
        onClick: () => handleReject(user.id),
        disabled: !hasPermission('users.reject'),
      },
    ] : []),
    ...(user.status === 'active' ? [
      {
        key: 'suspend',
        icon: <CloseCircleOutlined />,
        label: 'Suspend',
        onClick: () => handleSuspend(user.id),
        disabled: !hasPermission('users.suspend'),
      },
    ] : []),
  ];

  const columns = [
    {
      title: 'User',
      key: 'user',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <UserOutlined className="text-blue-600" />
          </div>
          <div>
            <div className="font-medium">{record.name}</div>
            <div className="text-sm text-gray-500">{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Tag color={getRoleColor(role)}>
          {role.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Company',
      dataIndex: 'company',
      key: 'company',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Profile',
      dataIndex: 'profileCompletion',
      key: 'profileCompletion',
      render: (completion) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${completion}%` }}
            ></div>
          </div>
          <span className="text-sm">{completion}%</span>
        </div>
      ),
    },
    {
      title: 'Join Date',
      dataIndex: 'joinDate',
      key: 'joinDate',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.status === 'pending' && hasPermission('users.approve') && (
            <>
              <Tooltip title="Approve">
                <Button 
                  type="primary" 
                  size="small" 
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleApprove(record.id)}
                />
              </Tooltip>
              <Tooltip title="Reject">
                <Button 
                  danger 
                  size="small" 
                  icon={<CloseCircleOutlined />}
                  onClick={() => handleReject(record.id)}
                />
              </Tooltip>
            </>
          )}
          <Dropdown menu={{ items: userMenuItems(record) }} trigger={['click']}>
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const filteredUsers = users.filter(user => {
    const matchesRole = filters.role === 'all' || user.role === filters.role;
    const matchesStatus = filters.status === 'all' || user.status === filters.status;
    const matchesSearch = !filters.search || 
      user.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      user.email.toLowerCase().includes(filters.search.toLowerCase()) ||
      user.company.toLowerCase().includes(filters.search.toLowerCase());
    
    return matchesRole && matchesStatus && matchesSearch;
  });

  const stats = {
    total: users.length,
    pending: users.filter(u => u.status === 'pending').length,
    active: users.filter(u => u.status === 'active').length,
    suspended: users.filter(u => u.status === 'suspended').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            User Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage platform users and approvals
          </p>
        </div>
        <Space>
          {hasPermission('users.export') && (
            <Button icon={<ExportOutlined />}>
              Export Users
            </Button>
          )}
          {hasPermission('users.create') && (
            <Button type="primary" icon={<PlusOutlined />}>
              Add User
            </Button>
          )}
        </Space>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats.total}
              prefix={<Users className="w-4 h-4" />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Pending Approval"
              value={stats.pending}
              prefix={<Clock className="w-4 h-4" />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={stats.active}
              prefix={<UserCheck className="w-4 h-4" />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Suspended"
              value={stats.suspended}
              prefix={<UserX className="w-4 h-4" />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Select
              value={filters.role}
              onChange={(value) => setFilters({ ...filters, role: value })}
              style={{ width: '100%' }}
            >
              <Option value="all">All Roles</Option>
              <Option value="company">Company</Option>
              <Option value="interviewer">Interviewer</Option>
              <Option value="super_admin">Super Admin</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              style={{ width: '100%' }}
            >
              <Option value="all">All Status</Option>
              <Option value="active">Active</Option>
              <Option value="pending">Pending</Option>
              <Option value="suspended">Suspended</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8}>
            <div className="text-sm text-gray-500">
              Showing {filteredUsers.length} of {users.length} users
            </div>
          </Col>
        </Row>
      </Card>

      {/* Users Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredUsers}
          loading={loading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            total: filteredUsers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} users`,
          }}
        />
      </Card>

      {/* User Details Modal */}
      <Modal
        title="User Details"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedUser && (
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <UserOutlined className="text-2xl text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium">{selectedUser.name}</h3>
                <p className="text-gray-500">{selectedUser.email}</p>
                <div className="flex space-x-2 mt-2">
                  <Tag color={getRoleColor(selectedUser.role)}>
                    {selectedUser.role.replace('_', ' ').toUpperCase()}
                  </Tag>
                  <Tag color={getStatusColor(selectedUser.status)}>
                    {selectedUser.status.toUpperCase()}
                  </Tag>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Company</label>
                <p className="mt-1">{selectedUser.company}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Join Date</label>
                <p className="mt-1">{new Date(selectedUser.joinDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Login</label>
                <p className="mt-1">{selectedUser.lastLogin}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Profile Completion</label>
                <p className="mt-1">{selectedUser.profileCompletion}%</p>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UserManagement;
