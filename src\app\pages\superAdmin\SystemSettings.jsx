import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Space } from 'antd';
import { SettingOutlined, SaveOutlined } from '@ant-design/icons';

const SystemSettings = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            System Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configure platform-wide settings and preferences
          </p>
        </div>
        <Space>
          <Button type="primary" icon={<SaveOutlined />}>
            Save Changes
          </Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <SettingOutlined className="text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            System Settings Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will allow you to configure platform-wide settings, feature toggles, and system preferences.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default SystemSettings;
