import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Space } from 'antd';
import { TeamOutlined, PlusOutlined } from '@ant-design/icons';

const TeamManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Team Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Organize teams and manage role-based permissions
          </p>
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />}>
            Create Team
          </Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <TeamOutlined className="text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Team Management Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will allow you to create and manage teams with role-based permissions.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default TeamManagement;
