import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Space } from 'antd';
import { CalendarOutlined, PlusOutlined } from '@ant-design/icons';

const InterviewManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Interview Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage platform interviews
          </p>
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />}>
            Schedule Interview
          </Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <CalendarOutlined className="text-6xl text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Interview Management Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will allow you to monitor, reassign, and manage all platform interviews.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default InterviewManagement;
