import React from 'react';
import { <PERSON>, Button, Space, Form, Input, Switch, Row, Col } from 'antd';
import { UserOutlined, SaveOutlined, SecurityScanOutlined } from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';

const SuperAdminProfile = () => {
  const { profile, adminLevel, permissions, teamMemberships } = useAuth();
  const [form] = Form.useForm();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Profile
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your super admin profile and security settings
          </p>
        </div>
        <Space>
          <Button type="primary" icon={<SaveOutlined />}>
            Save Changes
          </Button>
        </Space>
      </div>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Profile Information">
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                full_name: profile?.full_name || '',
                email: profile?.email || '',
                phone_number: profile?.phone_number || '',
              }}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Full Name"
                    name="full_name"
                    rules={[{ required: true, message: 'Please enter your full name' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Email"
                    name="email"
                    rules={[
                      { required: true, message: 'Please enter your email' },
                      { type: 'email', message: 'Please enter a valid email' }
                    ]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Phone Number"
                    name="phone_number"
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Admin Level"
                  >
                    <Input value={adminLevel || 'admin'} disabled />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>

          <Card title="Security Settings" className="mt-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Two-Factor Authentication</h4>
                  <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                </div>
                <Switch />
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Session Timeout</h4>
                  <p className="text-sm text-gray-500">Automatically log out after inactivity</p>
                </div>
                <Input style={{ width: 120 }} defaultValue="480" suffix="min" />
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium">IP Restrictions</h4>
                  <p className="text-sm text-gray-500">Restrict access to specific IP addresses</p>
                </div>
                <Button icon={<SecurityScanOutlined />}>Configure</Button>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Admin Status">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-green-600">Super Admin</h4>
                <p className="text-sm text-gray-500">Full platform access</p>
              </div>
              <div>
                <h4 className="font-medium">Permissions</h4>
                <p className="text-sm text-gray-500">{permissions?.length || 0} permissions granted</p>
              </div>
              <div>
                <h4 className="font-medium">Team Memberships</h4>
                <p className="text-sm text-gray-500">{teamMemberships?.length || 0} teams</p>
              </div>
            </div>
          </Card>

          <Card title="Recent Activity" className="mt-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center text-sm">
                <span>Last login</span>
                <span className="text-gray-500">2 hours ago</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span>Password changed</span>
                <span className="text-gray-500">1 week ago</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span>Profile updated</span>
                <span className="text-gray-500">2 weeks ago</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SuperAdminProfile;
