import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-react';
import App from '@/App';

import './styles/globals.css';
import 'antd/dist/reset.css';

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error('Missing Publishable Key');
}

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.Fragment>
    <React.StrictMode>
      <ClerkProvider
        publishableKey={PUBLISHABLE_KEY}
        afterSignOutUrl="/"
      >
        <App />
      </ClerkProvider>
    </React.StrictMode>
  </React.Fragment>
);
